import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createServiceClient } from '@/lib/supabase';

// GET /api/credits - Get credit balance and recent transactions
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    const supabase = createServiceClient();

    // Get or create user in users table
    let { data: userData } = await supabase
      .from('users')
      .select('id, organization_id')
      .eq('email', session.user.email)
      .single();

    // If user doesn't exist in users table, create them
    if (!userData) {
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          email: session.user.email,
          name: session.user.name || 'User',
          avatar_url: session.user.image
        })
        .select('id, organization_id')
        .single();

      if (createError) {
        console.error('Error creating user:', createError);
        return NextResponse.json(
          { error: 'Failed to create user', success: false },
          { status: 500 }
        );
      }
      userData = newUser;
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organizationId') || userData.organization_id;

    // If no organization, return default credits structure
    if (!organizationId) {
      return NextResponse.json({
        success: true,
        data: {
          credits: { balance: 100 }, // Default credits for users without organization
          recentTransactions: []
        }
      });
    }

    // Get credit balance
    const { data: credits, error: creditsError } = await supabase
      .from('credits')
      .select('*')
      .eq('organization_id', organizationId)
      .single();

    if (creditsError && creditsError.code !== 'PGRST116') { // Not found is OK
      console.error('Credits fetch error:', creditsError);
      return NextResponse.json(
        { error: 'Failed to fetch credits', success: false },
        { status: 500 }
      );
    }

    // If no credits record exists, create one with 0 balance
    let creditBalance = credits;
    if (!credits) {
      const { data: newCredits, error: createError } = await supabase
        .from('credits')
        .insert({
          organization_id: organizationId,
          balance: 0,
          total_purchased: 0,
          total_spent: 0,
        })
        .select()
        .single();

      if (createError) {
        console.error('Credits creation error:', createError);
        return NextResponse.json(
          { error: 'Failed to initialize credits', success: false },
          { status: 500 }
        );
      }
      creditBalance = newCredits;
    }

    // Get recent transactions (last 10)
    const { data: recentTransactions, error: transactionsError } = await supabase
      .from('credit_transactions')
      .select(`
        *,
        user:users(id, name, email),
        project:projects(id, name),
        chapter:chapters(id, title)
      `)
      .eq('organization_id', organizationId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (transactionsError) {
      console.error('Transactions fetch error:', transactionsError);
      return NextResponse.json(
        { error: 'Failed to fetch transactions', success: false },
        { status: 500 }
      );
    }

    // Calculate usage statistics
    const now = new Date();
    const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

    const { data: thisMonthUsage } = await supabase
      .from('credit_transactions')
      .select('amount')
      .eq('organization_id', organizationId)
      .lt('amount', 0) // Only deductions
      .gte('created_at', thisMonthStart.toISOString());

    const { data: lastMonthUsage } = await supabase
      .from('credit_transactions')
      .select('amount')
      .eq('organization_id', organizationId)
      .lt('amount', 0) // Only deductions
      .gte('created_at', lastMonthStart.toISOString())
      .lte('created_at', lastMonthEnd.toISOString());

    const thisMonth = Math.abs(thisMonthUsage?.reduce((sum, t) => sum + t.amount, 0) || 0);
    const lastMonth = Math.abs(lastMonthUsage?.reduce((sum, t) => sum + t.amount, 0) || 0);

    const usage = {
      thisMonth,
      lastMonth,
      total: creditBalance.total_spent,
    };

    return NextResponse.json({
      success: true,
      data: {
        credits: creditBalance,
        recentTransactions: recentTransactions || [],
        usage,
      },
    });

  } catch (error) {
    console.error('API error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
