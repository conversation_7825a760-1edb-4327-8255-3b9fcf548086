import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { id: projectId } = await params;

    // Get the current user session using NextAuth
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get user ID from session
    const { data: userData } = await supabase
      .from('users')
      .select('id')
      .eq('email', session.user.email)
      .single();

    if (!userData) {
      return NextResponse.json(
        { error: 'User not found', success: false },
        { status: 404 }
      );
    }

    // Get project with related data and verify user access
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select(`
        *,
        created_by_user:users!projects_created_by_fkey(id, name, email, avatar_url),
        project_manager:users!projects_project_manager_id_fkey(id, name, email, avatar_url),
        project_members!inner(user_id)
      `)
      .eq('id', projectId)
      .or(`created_by.eq.${userData.id},project_manager_id.eq.${userData.id},project_members.user_id.eq.${userData.id}`)
      .single();

    if (projectError) {
      console.error('Project fetch error:', projectError);
      return NextResponse.json(
        { error: 'Project not found', success: false },
        { status: 404 }
      );
    }

    // Get project team members
    const { data: teamMembers, error: teamError } = await supabase
      .from('project_members')
      .select(`
        *,
        user:users(id, name, email, avatar_url)
      `)
      .eq('project_id', projectId);

    if (teamError) {
      console.error('Team members fetch error:', teamError);
    }

    // Get project files/documents
    const { data: documents, error: documentsError } = await supabase
      .from('source_documents')
      .select('*')
      .eq('project_id', projectId);

    if (documentsError) {
      console.error('Documents fetch error:', documentsError);
    }

    // Get project progress stats
    const { data: progressStats, error: progressError } = await supabase
      .from('translation_segments')
      .select('status')
      .eq('project_id', projectId);

    if (progressError) {
      console.error('Progress stats fetch error:', progressError);
    }

    // Calculate progress
    const totalSegments = progressStats?.length || 0;
    const completedSegments = progressStats?.filter(s => s.status === 'translated').length || 0;
    const reviewedSegments = progressStats?.filter(s => s.status === 'reviewed').length || 0;
    const approvedSegments = progressStats?.filter(s => s.status === 'approved').length || 0;
    const pendingSegments = progressStats?.filter(s => s.status === 'pending').length || 0;
    const inProgressSegments = progressStats?.filter(s => s.status === 'in_progress').length || 0;

    // Get terminology entries for this project
    const { data: terminology, error: terminologyError } = await supabase
      .from('terminology_entries')
      .select('*')
      .eq('project_id', projectId);

    if (terminologyError) {
      console.error('Terminology fetch error:', terminologyError);
    }

    // Get recent activity (project activity log)
    const { data: recentActivity, error: activityError } = await supabase
      .from('project_activity')
      .select(`
        *,
        user:users(name, avatar_url)
      `)
      .eq('project_id', projectId)
      .order('created_at', { ascending: false })
      .limit(10);

    if (activityError) {
      console.error('Activity fetch error:', activityError);
    }

    // Format the response
    const response = {
      project: {
        ...project,
        totalSegments,
        completedSegments,
        reviewedSegments,
        approvedSegments,
      },
      documents: documents || [],
      team: teamMembers || [],
      progress: {
        totalSegments,
        pendingSegments,
        inProgressSegments,
        translatedSegments: completedSegments,
        reviewedSegments,
        approvedSegments,
      },
      terminology: terminology || [],
      recentActivity: recentActivity || [],
      success: true,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { id: projectId } = await params;
    const body = await request.json();

    // Get the current user session using NextAuth
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get user ID from session
    const { data: userData } = await supabase
      .from('users')
      .select('id')
      .eq('email', session.user.email)
      .single();

    if (!userData) {
      return NextResponse.json(
        { error: 'User not found', success: false },
        { status: 404 }
      );
    }

    // Verify user has permission to update this project
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id, created_by, project_manager_id')
      .eq('id', projectId)
      .or(`created_by.eq.${userData.id},project_manager_id.eq.${userData.id}`)
      .single();

    if (!projectAccess) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Update the project
    const { data: project, error: updateError } = await supabase
      .from('projects')
      .update({
        ...body,
        updated_at: new Date().toISOString(),
      })
      .eq('id', projectId)
      .select()
      .single();

    if (updateError) {
      console.error('Project update error:', updateError);
      return NextResponse.json(
        { error: 'Failed to update project', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      project,
      success: true,
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params as required by Next.js 15
    const { id: projectId } = await params;

    // Get the current user session using NextAuth
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized', success: false },
        { status: 401 }
      );
    }

    // Get user ID from session
    const { data: userData } = await supabase
      .from('users')
      .select('id')
      .eq('email', session.user.email)
      .single();

    if (!userData) {
      return NextResponse.json(
        { error: 'User not found', success: false },
        { status: 404 }
      );
    }

    // Verify user has permission to delete this project (only creator or project manager)
    const { data: projectAccess } = await supabase
      .from('projects')
      .select('id, created_by, project_manager_id')
      .eq('id', projectId)
      .or(`created_by.eq.${userData.id},project_manager_id.eq.${userData.id}`)
      .single();

    if (!projectAccess) {
      return NextResponse.json(
        { error: 'Project not found or access denied', success: false },
        { status: 404 }
      );
    }

    // Delete the project (cascade will handle related records)
    const { error: deleteError } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);

    if (deleteError) {
      console.error('Project delete error:', deleteError);
      return NextResponse.json(
        { error: 'Failed to delete project', success: false },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
    });
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error', success: false },
      { status: 500 }
    );
  }
}
